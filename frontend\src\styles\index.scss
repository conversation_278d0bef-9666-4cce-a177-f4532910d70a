// 全局样式文件

// 变量定义
@use './variables.scss' as *;

// 现代化CSS重置
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px; // 提升基础字体大小
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
  height: 100%;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
               'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
               'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑',
               sans-serif;
  color: var(--el-text-color-primary);
  background-color: var(--el-bg-color);
  min-height: 100%;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  width: 100%;
}

// 确保应用容器没有边距
#app {
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 100vh;
}

// 现代化链接样式
a {
  color: var(--el-color-primary);
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &:hover {
    color: var(--el-color-primary-dark-2);
  }

  &:focus-visible {
    outline: 2px solid var(--el-color-primary);
    outline-offset: 2px;
    border-radius: 4px;
  }
}

// 现代化按钮样式增强
.el-button {
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0);
  }

  &.el-button--large {
    padding: 14px 28px;
    font-size: 16px;
    border-radius: 10px;
  }

  &.el-button--small {
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 6px;
  }

  // 主要按钮样式
  &.el-button--primary {
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-dark-2));
    border: none;

    &:hover {
      background: linear-gradient(135deg, var(--el-color-primary-light-3), var(--el-color-primary));
      box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
    }
  }

  // 成功按钮样式
  &.el-button--success {
    background: linear-gradient(135deg, var(--el-color-success), var(--el-color-success-dark-2));
    border: none;

    &:hover {
      background: linear-gradient(135deg, var(--el-color-success-light-3), var(--el-color-success));
      box-shadow: 0 6px 20px rgba(103, 194, 58, 0.4);
    }
  }
}

// 现代化卡片样式
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 24px;
  margin-bottom: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-primary-light-3));
    transform: scaleX(0);
    transition: transform 0.3s ease;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--el-color-primary-light-7);

    &::before {
      transform: scaleX(1);
    }
  }

  // 卡片变体
  &.card-elevated {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }

  &.card-bordered {
    border: 2px solid var(--el-border-color-light);
  }

  &.card-primary {
    border-color: var(--el-color-primary-light-7);
    background: var(--el-color-primary-light-9);
  }
}

// 现代化页面容器
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(0,0,0,0.02)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
  }
}

.content-wrapper {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

// 现代化页面标题
.page-title {
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 700;
  color: var(--el-text-color-primary);
  margin-bottom: 2rem;
  position: relative;
  display: inline-block;

  &::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-primary-light-3));
    border-radius: 2px;
  }

  // 标题变体
  &.title-centered {
    text-align: center;
    width: 100%;

    &::after {
      left: 50%;
      transform: translateX(-50%);
    }
  }

  &.title-large {
    font-size: clamp(2rem, 5vw, 3.5rem);
    margin-bottom: 3rem;

    &::after {
      width: 80px;
      height: 6px;
    }
  }
}

// 现代化表单样式增强
.el-input {
  .el-input__wrapper {
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.is-focus {
      box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
    }
  }
}

.el-textarea {
  .el-textarea__inner {
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &:focus {
      box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
    }
  }
}

// 现代化选择器样式
.el-select {
  .el-select__wrapper {
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &.is-focused {
      box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
    }
  }
}

// 工具类
.text-gradient {
  background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-dark-2));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.shadow-soft {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.shadow-medium {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.shadow-strong {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.border-gradient {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 1px;
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-light-3));
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
  }
}

// 动画类
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// 动画工具类
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

// 延迟动画
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }

// 响应式工具类
.container-fluid {
  width: 100%;
  padding: 0 1rem;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;

  @media (max-width: 768px) {
    padding: 0 1rem;
  }
}

.container-sm {
  max-width: 640px;
  margin: 0 auto;
  padding: 0 1rem;
}

.container-md {
  max-width: 768px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.container-lg {
  max-width: 1024px;
  margin: 0 auto;
  padding: 0 2rem;
}

.container-xl {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 2rem;
}

// 间距工具类
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

.space-x-1 > * + * { margin-left: 0.25rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-x-8 > * + * { margin-left: 2rem; }

// 显示/隐藏工具类
.hidden { display: none !important; }
.block { display: block !important; }
.inline { display: inline !important; }
.inline-block { display: inline-block !important; }
.flex { display: flex !important; }
.inline-flex { display: inline-flex !important; }
.grid { display: grid !important; }

// Flexbox 工具类
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }

// 文本工具类
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }

.font-thin { font-weight: 100; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

// 颜色工具类
.text-primary { color: var(--el-color-primary); }
.text-success { color: var(--el-color-success); }
.text-warning { color: var(--el-color-warning); }
.text-danger { color: var(--el-color-danger); }
.text-info { color: var(--el-color-info); }

.bg-primary { background-color: var(--el-color-primary); }
.bg-success { background-color: var(--el-color-success); }
.bg-warning { background-color: var(--el-color-warning); }
.bg-danger { background-color: var(--el-color-danger); }
.bg-info { background-color: var(--el-color-info); }

// 圆角工具类
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: 0.125rem; }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-3xl { border-radius: 1.5rem; }
.rounded-full { border-radius: 9999px; }

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .page-container {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }

  .card {
    background: #1e293b;
    border-color: #334155;
    color: #f1f5f9;

    &:hover {
      border-color: var(--el-color-primary-light-5);
    }
  }

  .text-gradient {
    background: linear-gradient(135deg, var(--el-color-primary-light-3), var(--el-color-primary-light-5));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

// 打印样式
@media print {
  .no-print {
    display: none !important;
  }

  .page-container {
    background: white !important;
  }

  .card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }

  a {
    color: #000 !important;
    text-decoration: underline !important;
  }
}

// 无障碍支持
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .card {
    border: 2px solid var(--el-text-color-primary);
  }

  .el-button {
    border-width: 2px;
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--el-bg-color-page);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color-dark);
  border-radius: 4px;
  transition: background 0.3s;

  &:hover {
    background: var(--el-text-color-secondary);
  }
}

// Firefox 滚动条
* {
  scrollbar-width: thin;
  scrollbar-color: var(--el-border-color-dark) var(--el-bg-color-page);
}

// 表单样式增强
.form-container {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

  .el-form-item__label {
    font-weight: 500;
  }
}

// 表格样式增强
.el-table {
  border-radius: 8px;
  overflow: hidden;

  .el-table__header {
    background: #f8f9fa;
  }

  .el-table__row:hover {
    background: #f5f7fa;
  }
}

// 分页样式
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

// 加载状态
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  flex-direction: column;

  .loading-text {
    margin-top: 15px;
    color: var(--el-text-color-regular);
  }
}

// 空状态
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--el-text-color-regular);

  .empty-icon {
    font-size: 64px;
    color: var(--el-text-color-placeholder);
    margin-bottom: 20px;
  }

  .empty-title {
    font-size: 18px;
    margin-bottom: 10px;
    color: var(--el-text-color-regular);
  }

  .empty-description {
    font-size: 14px;
    color: var(--el-text-color-placeholder);
  }
}

// 统计卡片
.stat-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;

  &:hover {
    transform: translateY(-2px);
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 24px;
    color: white;
  }

  .stat-value {
    font-size: 28px;
    font-weight: 700;
    color: var(--el-text-color-primary);
    margin-bottom: 8px;
  }

  .stat-label {
    font-size: 14px;
    color: var(--el-text-color-regular);
  }

  .stat-change {
    font-size: 12px;
    margin-top: 8px;

    &.positive {
      color: var(--el-color-success);
    }

    &.negative {
      color: var(--el-color-danger);
    }
  }
}

// 响应式网格
.grid {
  display: grid;
  gap: 20px;

  &.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  &.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  &.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-10 { margin-bottom: 10px; }
.mb-20 { margin-bottom: 20px; }
.mb-30 { margin-bottom: 30px; }

.mt-10 { margin-top: 10px; }
.mt-20 { margin-top: 20px; }
.mt-30 { margin-top: 30px; }

.p-10 { padding: 10px; }
.p-20 { padding: 20px; }
.p-30 { padding: 30px; }

.flex { display: flex; }
.flex-center { 
  display: flex; 
  align-items: center; 
  justify-content: center; 
}
.flex-between { 
  display: flex; 
  align-items: center; 
  justify-content: space-between; 
}

.w-full { width: 100%; }
.h-full { height: 100%; }

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
  
  &:hover {
    background: var(--el-border-color-darker);
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

// 响应式断点
@media (max-width: 768px) {
  .content-wrapper {
    padding: 15px;
  }

  .grid {
    grid-template-columns: 1fr;
  }

  .page-title {
    font-size: 20px;
  }

  .form-container {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .content-wrapper {
    padding: 10px;
  }

  .card {
    padding: 15px;
  }

  .stat-card {
    padding: 20px;
  }
}
